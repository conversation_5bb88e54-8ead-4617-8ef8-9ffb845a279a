﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AssemblyName>IMYSHook</AssemblyName>
    <Description>IMYSHook</Description>
    <Version>1.0.5</Version>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <LangVersion>latest</LangVersion>
    <RestoreAdditionalProjectSources>
      https://api.nuget.org/v3/index.json;
      https://nuget.bepinex.dev/v3/index.json
    </RestoreAdditionalProjectSources>
    <RootNamespace>IMYSHook</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BepInEx.Unity.IL2CPP" Version="6.0.0-be.*" IncludeAssets="compile" />
    <PackageReference Include="BepInEx.PluginInfoProps" Version="2.*" />
    <Reference Include="Assembly-CSharp">
      <HintPath>lib/Assembly-CSharp.dll</HintPath>
    </Reference>
    <Reference Include="Il2Cppmscorlib">
      <HintPath>lib/Il2Cppmscorlib.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>lib/Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>lib/nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="Purchasing.Common">
      <HintPath>lib/Purchasing.Common.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core">
      <HintPath>lib/Unity.Services.Core.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core.Configuration">
      <HintPath>lib/Unity.Services.Core.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core.Device">
      <HintPath>lib/Unity.Services.Core.Device.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core.Environments">
      <HintPath>lib/Unity.Services.Core.Environments.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core.Environments.Internal">
      <HintPath>lib/Unity.Services.Core.Environments.Internal.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core.Internal">
      <HintPath>lib/Unity.Services.Core.Internal.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core.Registration">
      <HintPath>lib/Unity.Services.Core.Registration.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core.Scheduler">
      <HintPath>lib/Unity.Services.Core.Scheduler.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core.Telemetry">
      <HintPath>lib/Unity.Services.Core.Telemetry.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Services.Core.Threading">
      <HintPath>lib/Unity.Services.Core.Threading.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>lib/Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine">
      <HintPath>lib/UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>lib/UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>lib/UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>lib/UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>lib/UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>lib/UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>lib/UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>lib/UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>lib/UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>lib/UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>lib/UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>lib/UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>lib/UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>lib/UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>lib/UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>lib/UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>lib/UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>lib/UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>lib/UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>lib/UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>lib/UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>lib/UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>lib/UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>lib/UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>lib/UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>lib/UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>lib/UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>lib/UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>lib/UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Purchasing">
      <HintPath>lib/UnityEngine.Purchasing.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Purchasing.AppleCore">
      <HintPath>lib/UnityEngine.Purchasing.AppleCore.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Purchasing.AppleMacosStub">
      <HintPath>lib/UnityEngine.Purchasing.AppleMacosStub.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Purchasing.AppleStub">
      <HintPath>lib/UnityEngine.Purchasing.AppleStub.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Purchasing.SecurityCore">
      <HintPath>lib/UnityEngine.Purchasing.SecurityCore.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Purchasing.SecurityStub">
      <HintPath>lib/UnityEngine.Purchasing.SecurityStub.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Purchasing.Stores">
      <HintPath>lib/UnityEngine.Purchasing.Stores.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Purchasing.WinRTCore">
      <HintPath>lib/UnityEngine.Purchasing.WinRTCore.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Purchasing.WinRTStub">
      <HintPath>lib/UnityEngine.Purchasing.WinRTStub.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>lib/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>lib/UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>lib/UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>lib/UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>lib/UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>lib/UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>lib/UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>lib/UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>lib/UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>lib/UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>lib/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>lib/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>lib/UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>lib/UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>lib/UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>lib/UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>lib/UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>lib/UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>lib/UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>lib/UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>lib/UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>lib/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>lib/UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>lib/UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>lib/UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>lib/UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>lib/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>lib/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>lib/UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>lib/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>lib/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>lib/UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>lib/UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>lib/UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>lib/UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>lib/UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>lib/UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>lib/UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UniWebView-CSharp">
      <HintPath>lib/UniWebView-CSharp.dll</HintPath>
    </Reference>
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="copy config.json $(OutDir)&#xD;&#xA;copy SS_Notification.ps1 $(OutDir)" />
  </Target>
</Project>
